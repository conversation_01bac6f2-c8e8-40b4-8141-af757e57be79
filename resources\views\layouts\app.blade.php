<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', $settings['site_name'] ?? 'บริการจัดงานศพ')</title>
    <meta name="description" content="@yield('description', $settings['site_description'] ?? 'ให้เราดูแลในช่วงเวลาที่ยากลำบาก ด้วยความเคารพและเอาใจใส่')")
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Kanit:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ asset('css/funeral-style.css') }}" rel="stylesheet">

    <style>
        body {
            font-family: 'Inter', 'Kanit', sans-serif;
            background-color: #fafafa;
        }
    </style>
    
    @yield('styles')
</head>
<body>
    <!-- Navigation - สวยงามเรียบง่าย -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand fw-bold text-primary" href="{{ route('home') }}">
                <i class="fas fa-star me-2"></i>{{ $settings['site_name'] ?? 'SoloShop' }}
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}" href="{{ route('home') }}">หน้าหลัก</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('services') ? 'active' : '' }}" href="{{ route('services') }}">บริการ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('packages') ? 'active' : '' }}" href="{{ route('packages') }}">แพคเกจ</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('activities') ? 'active' : '' }}" href="{{ route('activities') }}">ผลงาน</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('contact') ? 'active' : '' }}" href="{{ route('contact') }}">ติดต่อเรา</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('admin.login') }}">
                            <i class="fas fa-user-shield me-1"></i>เข้าสู่ระบบ
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="footer mt-5 py-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5>{{ $settings['site_name'] ?? 'บริการจัดงานศพ' }}</h5>
                    <p>{{ $settings['site_description'] ?? 'ให้เราดูแลในช่วงเวลาที่ยากลำบาก ด้วยความเคารพและเอาใจใส่' }}</p>
                </div>
                <div class="col-md-4">
                    <h5>ติดต่อเรา</h5>
                    <p><i class="fas fa-phone"></i> {{ $settings['contact_phone'] ?? '02-xxx-xxxx' }}</p>
                    <p><i class="fas fa-envelope"></i> {{ $settings['contact_email'] ?? '<EMAIL>' }}</p>
                    <p><i class="fas fa-map-marker-alt"></i> {{ $settings['contact_address'] ?? 'ที่อยู่บริษัท' }}</p>
                </div>
                <div class="col-md-4">
                    <h5>ติดตามเรา</h5>
                    <div class="d-flex gap-3">
                        @if(!empty($settings['facebook_url']))
                        <a href="{{ $settings['facebook_url'] }}" class="text-white" target="_blank">
                            <i class="fab fa-facebook fa-2x"></i>
                        </a>
                        @endif
                        @if(!empty($settings['line_id']))
                        <a href="https://line.me/ti/p/{{ $settings['line_id'] }}" class="text-white" target="_blank">
                            <i class="fab fa-line fa-2x"></i>
                        </a>
                        @endif
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="text-center">
                <p>&copy; {{ date('Y') }} {{ $settings['site_name'] ?? 'บริการจัดงานศพ' }}. สงวนลิขสิทธิ์.</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    @yield('scripts')
</body>
</html>
