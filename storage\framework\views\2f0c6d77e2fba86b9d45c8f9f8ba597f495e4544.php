<?php $__env->startSection('title', 'ผลงาน - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ')); ?>

<?php $__env->startSection('content'); ?>
<!-- Page Header -->
<section class="hero-section">
    <div class="container">
        <div class="text-center">
            <h1 class="display-4 fw-bold mb-4">ผลงานการให้บริการ</h1>
            <p class="lead">ภาพบรรยากาศการให้บริการจัดงานศพที่ผ่านมา</p>
        </div>
    </div>
</section>

<!-- Activities Section -->
<section class="py-5">
    <div class="container">
        <?php if($activities->count() > 0): ?>
        <div class="row g-4">
            <?php $__currentLoopData = $activities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-6 col-lg-4">
                <div class="card service-card h-100 activity-card">
                    <!-- Cover Image with Overlay -->
                    <div class="card-image-container img-size-large position-relative">
                        <?php
                            $coverImage = $activity->images->where('is_cover', true)->first() ?? $activity->images->first();
                            $coverImagePath = $coverImage ? $coverImage->image_path : $activity->image;
                        ?>
                        <img src="<?php echo e(asset('storage/' . $coverImagePath)); ?>"
                             class="img-fit-contain activity-cover"
                             alt="<?php echo e($activity->title); ?>">
                        <div class="position-absolute top-0 start-0 w-100 h-100 bg-dark bg-opacity-25 d-flex align-items-center justify-content-center opacity-0 activity-overlay">
                            <div class="text-center text-white">
                                <i class="fas fa-eye fa-2x mb-2"></i>
                                <div>ดูรายละเอียด</div>
                                <?php if($activity->images->count() > 1): ?>
                                <small class="d-block mt-1"><?php echo e($activity->images->count()); ?> รูป</small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title"><?php echo e($activity->title); ?></h5>
                        <p class="card-text flex-grow-1"><?php echo e(Str::limit($activity->description, 100)); ?></p>

                        <div class="activity-meta mb-3">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-calendar text-primary me-2"></i>
                                <small class="text-muted"><?php echo e($activity->activity_date->format('d/m/Y')); ?></small>
                            </div>
                            <?php if($activity->location): ?>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                <small class="text-muted"><?php echo e(Str::limit($activity->location, 30)); ?></small>
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="mt-auto">
                            <a href="<?php echo e(route('activities.show', $activity->id)); ?>" class="btn btn-primary w-100">
                                <i class="fas fa-eye me-2"></i>ดูรายละเอียด
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-images fa-5x text-muted mb-4"></i>
            <h3 class="text-muted">ยังไม่มีผลงาน</h3>
            <p class="text-muted">กรุณาติดตามผลงานการให้บริการของเราในอนาคต</p>
            <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary">ติดต่อเรา</a>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Gallery Section -->
<?php if($activities->count() > 0): ?>
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">แกลเลอรี่ผลงาน</h2>
            <p class="text-muted">ภาพบรรยากาศการให้บริการจัดงานศพที่ผ่านมา</p>
        </div>
        
        <div class="row g-3">
            <?php $__currentLoopData = $activities->take(8); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-3 col-6">
                <div class="gallery-item position-relative overflow-hidden rounded">
                    <?php
                        $galleryCoverImage = $activity->images->where('is_cover', true)->first() ?? $activity->images->first();
                        $galleryCoverImagePath = $galleryCoverImage ? $galleryCoverImage->image_path : $activity->image;
                    ?>
                    <img src="<?php echo e(asset('storage/' . $galleryCoverImagePath)); ?>"
                         class="img-fluid w-100 gallery-image"
                         alt="<?php echo e($activity->title); ?>"
                         style="height: 200px; object-fit: cover; cursor: pointer; transition: transform 0.3s ease;"
                         data-bs-toggle="modal"
                         data-bs-target="#imageModal"
                         data-image="<?php echo e(asset('storage/' . $galleryCoverImagePath)); ?>"
                         data-title="<?php echo e($activity->title); ?>"
                         data-description="<?php echo e($activity->description); ?>"
                         data-date="<?php echo e($activity->activity_date->format('d/m/Y')); ?>"
                         data-location="<?php echo e($activity->location); ?>">
                    <div class="position-absolute bottom-0 start-0 w-100 bg-dark bg-opacity-75 text-white p-2 gallery-caption">
                        <small class="d-block fw-bold"><?php echo e(Str::limit($activity->title, 25)); ?></small>
                        <small class="text-white-50"><?php echo e($activity->activity_date->format('d/m/Y')); ?></small>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" class="img-fluid rounded mb-3" alt="">
                <p id="modalDescription" class="text-muted"></p>
                <div class="d-flex justify-content-center gap-4">
                    <div id="modalDate"></div>
                    <div id="modalLocation"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Contact CTA Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="fw-bold mb-4">ต้องการความช่วยเหลือหรือไม่?</h2>
                <p class="lead mb-4">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก ติดต่อเราได้ตลอด 24 ชั่วโมง</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary btn-lg">
                        <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                    </a>
                    <a href="tel:<?php echo e($settings['contact_phone'] ?? ''); ?>" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-phone me-2"></i><?php echo e($settings['contact_phone'] ?? '02-xxx-xxxx'); ?>

                    </a>
                </div>
                <div class="mt-4">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        บริการตลอด 24 ชั่วโมง ทุกวัน
                    </small>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Image modal functionality
    const imageModal = document.getElementById('imageModal');

    if (imageModal) {
        imageModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const image = button.getAttribute('data-image');
            const title = button.getAttribute('data-title');
            const description = button.getAttribute('data-description');
            const date = button.getAttribute('data-date');
            const location = button.getAttribute('data-location');

            document.getElementById('modalImage').src = image;
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalDescription').textContent = description;
            document.getElementById('modalDate').innerHTML = '<i class="fas fa-calendar text-primary me-2"></i>' + date;

            if (location) {
                document.getElementById('modalLocation').innerHTML = '<i class="fas fa-map-marker-alt text-primary me-2"></i>' + location;
            } else {
                document.getElementById('modalLocation').innerHTML = '';
            }
        });
    }

    // Activity card click functionality
    document.querySelectorAll('.activity-card').forEach(function(card) {
        card.addEventListener('click', function(e) {
            // Don't navigate if clicking on the button
            if (e.target.closest('.btn')) {
                return;
            }

            const link = card.querySelector('a[href*="activities"]');
            if (link) {
                window.location.href = link.href;
            }
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/frontend/activities.blade.php ENDPATH**/ ?>