@extends('layouts.admin')

@section('title', 'เพิ่มแพคเกจใหม่ - ระบบจัดการ')

@section('breadcrumb')
<li class="breadcrumb-item"><a href="{{ route('admin.packages') }}">จัดการแพคเกจ</a></li>
<li class="breadcrumb-item active">เพิ่มแพคเกจใหม่</li>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">เพิ่มแพคเกจใหม่</h1>
    <a href="{{ route('admin.packages') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>กลับ
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form action="{{ route('admin.packages.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">ชื่อแพคเกจ <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                               id="name" name="name" value="{{ old('name') }}" required>
                        @error('name')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">คำอธิบาย <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3" required>{{ old('description') }}</textarea>
                        @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="features" class="form-label">รายการที่รวมในแพคเกจ <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('features') is-invalid @enderror" 
                                  id="features" name="features" rows="6" required 
                                  placeholder="- รายการที่ 1&#10;- รายการที่ 2&#10;- รายการที่ 3">{{ old('features') }}</textarea>
                        @error('features')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">แต่ละรายการขึ้นบรรทัดใหม่</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="duration" class="form-label">ระยะเวลา</label>
                        <input type="text" class="form-control @error('duration') is-invalid @enderror"
                               id="duration" name="duration" value="{{ old('duration') }}"
                               placeholder="เช่น ครั้งเดียว, 1 ปี, 6 เดือน">
                        @error('duration')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="sort_order" class="form-label">ลำดับการแสดง</label>
                        <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                               id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0">
                        @error('sort_order')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="image" class="form-label">รูปภาพ</label>
                        <input type="file" class="form-control @error('image') is-invalid @enderror" 
                               id="image" name="image" accept="image/*">
                        @error('image')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" 
                                           value="1" {{ old('is_featured') ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_featured">
                                        แพคเกจแนะนำ
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        เปิดใช้งาน
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>บันทึก
                        </button>
                        <a href="{{ route('admin.packages') }}" class="btn btn-secondary">ยกเลิก</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">คำแนะนำ</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        ใช้ชื่อแพคเกจที่สื่อถึงกลุ่มเป้าหมาย
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        เขียนคำอธิบายที่ชัดเจนและน่าสนใจ
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        ระบุรายการที่รวมในแพคเกจอย่างละเอียด
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        ตั้งราคาที่เหมาะสมและแข่งขันได้
                    </li>
                    <li>
                        <i class="fas fa-lightbulb text-warning me-2"></i>
                        เลือกแพคเกจแนะนำเพื่อเน้นย้ำ
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
@endsection
