<?php $__env->startSection('title', 'ติดต่อเรา - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ')); ?>

<?php $__env->startSection('content'); ?>
<!-- Modern Hero Section -->
<section class="modern-hero-section">
    <div class="container">
        <div class="row align-items-center min-vh-50">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1 class="hero-title">ติดต่อเรา</h1>
                    <p class="hero-subtitle">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก</p>
                    <div class="hero-features">
                        <div class="feature-item">
                            <i class="fas fa-clock text-primary"></i>
                            <span>บริการตลอด 24 ชั่วโมง</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-phone text-primary"></i>
                            <span>ปรึกษาฟรี ไม่มีค่าใช้จ่าย</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-heart text-primary"></i>
                            <span>ดูแลด้วยความเข้าใจ</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-image">
                    <div class="contact-quick-actions">
                        <h4 class="mb-4">ติดต่อด่วน</h4>
                        <div class="quick-contact-grid">
                            <a href="tel:<?php echo e($settings['contact_phone'] ?? ''); ?>" class="quick-contact-item phone">
                                <i class="fas fa-phone"></i>
                                <span>โทรเลย</span>
                                <small><?php echo e($settings['contact_phone'] ?? '02-xxx-xxxx'); ?></small>
                            </a>
                            <?php if(!empty($settings['line_id'])): ?>
                            <a href="https://line.me/ti/p/<?php echo e($settings['line_id']); ?>" class="quick-contact-item line" target="_blank">
                                <i class="fab fa-line"></i>
                                <span>Line</span>
                                <small>แชทเลย</small>
                            </a>
                            <?php endif; ?>
                            <a href="mailto:<?php echo e($settings['contact_email'] ?? ''); ?>" class="quick-contact-item email">
                                <i class="fas fa-envelope"></i>
                                <span>อีเมล</span>
                                <small>ส่งข้อความ</small>
                            </a>
                            <?php if(!empty($settings['facebook_url'])): ?>
                            <a href="<?php echo e($settings['facebook_url']); ?>" class="quick-contact-item facebook" target="_blank">
                                <i class="fab fa-facebook"></i>
                                <span>Facebook</span>
                                <small>ติดตาม</small>
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Modern Contact Section -->
<section class="py-5 bg-light">
    <div class="container">
        <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show modern-alert" role="alert">
            <div class="d-flex align-items-center">
                <i class="fas fa-check-circle me-3 fs-4"></i>
                <div>
                    <strong>สำเร็จ!</strong> <?php echo e(session('success')); ?>

                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <div class="row g-5">
            <!-- Modern Contact Form -->
            <div class="col-lg-8">
                <div class="modern-card">
                    <div class="card-header">
                        <div class="d-flex align-items-center">
                            <div class="icon-wrapper me-3">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div>
                                <h3 class="mb-1">ส่งข้อความถึงเรา</h3>
                                <p class="text-muted mb-0">เราจะตอบกลับภายใน 24 ชั่วโมง</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <form action="<?php echo e(route('contact.store')); ?>" method="POST" class="modern-form">
                            <?php echo csrf_field(); ?>
                            <div class="row g-4">
                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="name" name="name" value="<?php echo e(old('name')); ?>" placeholder="ชื่อ-นามสกุล" required>
                                        <label for="name">ชื่อ-นามสกุล <span class="text-danger">*</span></label>
                                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="email" name="email" value="<?php echo e(old('email')); ?>" placeholder="อีเมล" required>
                                        <label for="email">อีเมล <span class="text-danger">*</span></label>
                                        <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <input type="tel" class="form-control <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="phone" name="phone" value="<?php echo e(old('phone')); ?>" placeholder="เบอร์โทรศัพท์">
                                        <label for="phone">เบอร์โทรศัพท์</label>
                                        <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-floating">
                                        <select class="form-select <?php $__errorArgs = ['subject'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="subject" name="subject" required>
                                            <option value="">เลือกหัวข้อ</option>
                                            <option value="สอบถามบริการ" <?php echo e(old('subject') == 'สอบถามบริการ' ? 'selected' : ''); ?>>สอบถามบริการ</option>
                                            <option value="ขอใบเสนอราคา" <?php echo e(old('subject') == 'ขอใบเสนอราคา' ? 'selected' : ''); ?>>ขอใบเสนอราคา</option>
                                            <option value="จองบริการ" <?php echo e(old('subject') == 'จองบริการ' ? 'selected' : ''); ?>>จองบริการ</option>
                                            <option value="ร้องเรียน/แนะนำ" <?php echo e(old('subject') == 'ร้องเรียน/แนะนำ' ? 'selected' : ''); ?>>ร้องเรียน/แนะนำ</option>
                                            <option value="อื่นๆ" <?php echo e(old('subject') == 'อื่นๆ' ? 'selected' : ''); ?>>อื่นๆ</option>
                                        </select>
                                        <label for="subject">หัวข้อ <span class="text-danger">*</span></label>
                                        <?php $__errorArgs = ['subject'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>

                                <div class="col-12">
                                    <div class="form-floating">
                                        <textarea class="form-control <?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                  id="message" name="message" style="height: 120px" placeholder="ข้อความ" required><?php echo e(old('message')); ?></textarea>
                                        <label for="message">ข้อความ <span class="text-danger">*</span></label>
                                        <?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>

                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary btn-lg modern-btn">
                                        <i class="fas fa-paper-plane me-2"></i>ส่งข้อความ
                                        <span class="btn-shine"></span>
                                    </button>
                                    <p class="text-muted mt-2 mb-0">
                                        <i class="fas fa-shield-alt me-1"></i>
                                        ข้อมูลของคุณจะถูกเก็บเป็นความลับ
                                    </p>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Modern Contact Info Sidebar -->
            <div class="col-lg-4">
                <!-- Contact Information Card -->
                <div class="modern-card mb-4">
                    <div class="card-header">
                        <div class="d-flex align-items-center">
                            <div class="icon-wrapper me-3">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <h4 class="mb-0">ข้อมูลติดต่อ</h4>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="contact-info-modern">
                            <!-- Address -->
                            <div class="contact-item">
                                <div class="contact-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div class="contact-details">
                                    <h6>ที่อยู่</h6>
                                    <p><?php echo e($settings['contact_address'] ?? 'ที่อยู่บริษัท'); ?></p>
                                    <a href="https://maps.google.com/?q=<?php echo e(urlencode($settings['contact_address'] ?? 'ที่อยู่บริษัท')); ?>"
                                       class="btn btn-outline-primary btn-sm" target="_blank">
                                        <i class="fas fa-directions me-1"></i>เส้นทาง
                                    </a>
                                </div>
                            </div>

                            <!-- Phone -->
                            <div class="contact-item">
                                <div class="contact-icon">
                                    <i class="fas fa-phone"></i>
                                </div>
                                <div class="contact-details">
                                    <h6>โทรศัพท์</h6>
                                    <p><?php echo e($settings['contact_phone'] ?? '02-xxx-xxxx'); ?></p>
                                    <a href="tel:<?php echo e($settings['contact_phone'] ?? ''); ?>" class="btn btn-primary btn-sm">
                                        <i class="fas fa-phone me-1"></i>โทรเลย
                                    </a>
                                </div>
                            </div>

                            <!-- Email -->
                            <div class="contact-item">
                                <div class="contact-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="contact-details">
                                    <h6>อีเมล</h6>
                                    <p><?php echo e($settings['contact_email'] ?? '<EMAIL>'); ?></p>
                                    <a href="mailto:<?php echo e($settings['contact_email'] ?? ''); ?>" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-envelope me-1"></i>ส่งอีเมล
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Business Hours Card -->
                <div class="modern-card mb-4">
                    <div class="card-header">
                        <div class="d-flex align-items-center">
                            <div class="icon-wrapper me-3">
                                <i class="fas fa-clock"></i>
                            </div>
                            <h4 class="mb-0">เวลาให้บริการ</h4>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="emergency-notice">
                            <div class="emergency-badge">
                                <i class="fas fa-phone-alt"></i>
                                <span>บริการฉุกเฉิน 24/7</span>
                            </div>
                        </div>

                        <div class="business-hours-modern">
                            <div class="hours-item">
                                <div class="hours-label">
                                    <i class="fas fa-building"></i>
                                    <span>สำนักงาน</span>
                                </div>
                                <div class="hours-time">08:00 - 17:00</div>
                            </div>
                            <div class="hours-item">
                                <div class="hours-label">
                                    <i class="fas fa-ambulance"></i>
                                    <span>บริการฉุกเฉิน</span>
                                </div>
                                <div class="hours-time emergency">24 ชั่วโมง</div>
                            </div>
                            <div class="hours-item">
                                <div class="hours-label">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>วันหยุดนักขัตฤกษ์</span>
                                </div>
                                <div class="hours-time available">ให้บริการ</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Social Media Card -->
                <?php if(!empty($settings['facebook_url']) || !empty($settings['line_id'])): ?>
                <div class="modern-card">
                    <div class="card-header">
                        <div class="d-flex align-items-center">
                            <div class="icon-wrapper me-3">
                                <i class="fas fa-share-alt"></i>
                            </div>
                            <h4 class="mb-0">ติดตามเรา</h4>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="social-links">
                            <?php if(!empty($settings['facebook_url'])): ?>
                            <a href="<?php echo e($settings['facebook_url']); ?>" class="social-link facebook" target="_blank">
                                <i class="fab fa-facebook-f"></i>
                                <span>Facebook</span>
                            </a>
                            <?php endif; ?>
                            <?php if(!empty($settings['line_id'])): ?>
                            <a href="https://line.me/ti/p/<?php echo e($settings['line_id']); ?>" class="social-link line" target="_blank">
                                <i class="fab fa-line"></i>
                                <span>Line Official</span>
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?php $__env->startSection('styles'); ?>
<style>
/* Modern Contact Page Styles - Funeral Theme */
.modern-hero-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
    color: #1e293b;
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.modern-hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(71, 85, 105, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: #1e293b;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    color: #475569;
    opacity: 0.9;
}

.hero-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 1rem;
    background: rgba(255,255,255,0.6);
    border-radius: 50px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(71, 85, 105, 0.1);
}

.feature-item i {
    font-size: 1.25rem;
    color: #475569;
}

.contact-quick-actions {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    padding: 2rem;
    color: #1e293b;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(71, 85, 105, 0.1);
}

.quick-contact-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.quick-contact-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem 1rem;
    border-radius: 15px;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.quick-contact-item.phone {
    background: linear-gradient(135deg, #64748b, #475569);
    color: white;
}

.quick-contact-item.line {
    background: linear-gradient(135deg, #00C300, #00B300);
    color: white;
}

.quick-contact-item.email {
    background: linear-gradient(135deg, #64748b, #475569);
    color: white;
}

.quick-contact-item.facebook {
    background: linear-gradient(135deg, #1877F2, #166FE5);
    color: white;
}

.quick-contact-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(71, 85, 105, 0.3);
    color: white;
}

.quick-contact-item i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.quick-contact-item span {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.quick-contact-item small {
    font-size: 0.8rem;
    opacity: 0.8;
}

.modern-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: none;
    overflow: hidden;
    transition: all 0.4s ease;
    backdrop-filter: blur(10px);
}

.modern-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    background: rgba(255, 255, 255, 0.95);
}

.modern-card .card-header {
    background: linear-gradient(135deg, #f8fafc, #f1f5f9);
    border: none;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid rgba(71, 85, 105, 0.1);
}

.modern-card .card-body {
    padding: 2rem;
}

.icon-wrapper {
    width: 50px;
    height: 50px;
    border-radius: 15px;
    background: linear-gradient(135deg, #64748b, #475569);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.modern-form .form-floating {
    margin-bottom: 0;
}

.modern-form .form-control,
.modern-form .form-select {
    border-radius: 15px;
    border: 2px solid #e9ecef;
    padding: 1rem 1.25rem;
    transition: all 0.3s ease;
}

.modern-form .form-control:focus,
.modern-form .form-select:focus {
    border-color: #475569;
    box-shadow: 0 0 0 0.2rem rgba(71, 85, 105, 0.25);
}

.modern-btn {
    border-radius: 50px;
    padding: 1rem 2rem;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #64748b, #475569);
    border: none;
    transition: all 0.3s ease;
}

.modern-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(71, 85, 105, 0.3);
    background: linear-gradient(135deg, #475569, #334155);
}

.btn-shine {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s ease;
}

.modern-btn:hover .btn-shine {
    left: 100%;
}

.contact-info-modern .contact-item {
    display: flex;
    gap: 1rem;
    padding: 1.5rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.contact-info-modern .contact-item:last-child {
    border-bottom: none;
}

.contact-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #64748b, #475569);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.contact-details h6 {
    margin-bottom: 0.5rem;
    color: #1e293b;
    font-weight: 600;
}

.contact-details p {
    margin-bottom: 1rem;
    color: #64748b;
    line-height: 1.5;
}

.emergency-notice {
    text-align: center;
    margin-bottom: 1.5rem;
}

.emergency-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-weight: 600;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.business-hours-modern .hours-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(71, 85, 105, 0.1);
}

.business-hours-modern .hours-item:last-child {
    border-bottom: none;
}

.hours-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #1e293b;
}

.hours-label i {
    color: #475569;
    width: 20px;
}

.hours-time {
    font-weight: 600;
    color: #64748b;
}

.hours-time.emergency {
    color: #dc2626;
}

.hours-time.available {
    color: #059669;
}

.social-links {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-radius: 15px;
    text-decoration: none;
    transition: all 0.3s ease;
    color: white;
}

.social-link.facebook {
    background: linear-gradient(135deg, #1877F2, #166FE5);
}

.social-link.line {
    background: linear-gradient(135deg, #00C300, #00B300);
}

.social-link:hover {
    transform: translateX(10px);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.modern-alert {
    border-radius: 15px;
    border: none;
    padding: 1.5rem;
    margin-bottom: 2rem;
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .quick-contact-grid {
        grid-template-columns: 1fr;
    }

    .hero-features {
        margin-top: 2rem;
    }

    .contact-quick-actions {
        margin-top: 2rem;
    }
}
</style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/frontend/contact.blade.php ENDPATH**/ ?>