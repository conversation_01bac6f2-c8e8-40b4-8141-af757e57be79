@extends('layouts.admin')

@section('title', 'ข้อความติดต่อ - ระบบจัดการ')

@section('breadcrumb')
<li class="breadcrumb-item active">ข้อความติดต่อ</li>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">ข้อความติดต่อ</h1>
    <div class="d-flex gap-2">
        <span class="badge bg-warning text-dark fs-6">
            ข้อความใหม่: {{ $contacts->where('is_read', false)->count() }}
        </span>
        <span class="badge bg-info fs-6">
            ทั้งหมด: {{ $contacts->count() }}
        </span>
    </div>
</div>

<div class="card">
    <div class="card-body">
        @if($contacts->count() > 0)
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>สถานะ</th>
                        <th>ชื่อ</th>
                        <th>อีเมล</th>
                        <th>โทรศัพท์</th>
                        <th>หัวข้อ</th>
                        <th>วันที่</th>
                        <th>การจัดการ</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($contacts as $contact)
                    <tr class="{{ !$contact->is_read ? 'table-warning' : '' }}">
                        <td>
                            @if($contact->is_read)
                            <span class="badge bg-success">อ่านแล้ว</span>
                            @else
                            <span class="badge bg-warning text-dark">ใหม่</span>
                            @endif
                        </td>
                        <td>
                            <strong>{{ $contact->name }}</strong>
                        </td>
                        <td>
                            <a href="mailto:{{ $contact->email }}" class="text-decoration-none">
                                {{ $contact->email }}
                            </a>
                        </td>
                        <td>
                            @if($contact->phone)
                            <a href="tel:{{ $contact->phone }}" class="text-decoration-none">
                                {{ $contact->phone }}
                            </a>
                            @else
                            <span class="text-muted">-</span>
                            @endif
                        </td>
                        <td>
                            {{ Str::limit($contact->subject, 30) }}
                        </td>
                        <td>
                            <small>{{ $contact->created_at->format('d/m/Y H:i') }}</small>
                            <br>
                            <small class="text-muted">{{ $contact->created_at->diffForHumans() }}</small>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ route('admin.contacts.show', $contact->id) }}" 
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> ดู
                                </a>
                                <form action="{{ route('admin.contacts.delete', $contact->id) }}" 
                                      method="POST" class="d-inline"
                                      onsubmit="return confirm('คุณแน่ใจหรือไม่ที่จะลบข้อความนี้?')">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @else
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-5x text-muted mb-3"></i>
            <h4 class="text-muted">ยังไม่มีข้อความติดต่อ</h4>
            <p class="text-muted">เมื่อมีลูกค้าติดต่อเข้ามา ข้อความจะแสดงที่นี่</p>
        </div>
        @endif
    </div>
</div>

@if($contacts->count() > 0)
<!-- Summary Cards -->
<div class="row g-4 mt-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">ข้อความทั้งหมด</h6>
                        <h3 class="mb-0">{{ $contacts->count() }}</h3>
                    </div>
                    <i class="fas fa-envelope fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">ข้อความใหม่</h6>
                        <h3 class="mb-0">{{ $contacts->where('is_read', false)->count() }}</h3>
                    </div>
                    <i class="fas fa-bell fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">อ่านแล้ว</h6>
                        <h3 class="mb-0">{{ $contacts->where('is_read', true)->count() }}</h3>
                    </div>
                    <i class="fas fa-check fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">วันนี้</h6>
                        <h3 class="mb-0">{{ $contacts->where('created_at', '>=', today())->count() }}</h3>
                    </div>
                    <i class="fas fa-calendar-day fa-2x opacity-75"></i>
                </div>
            </div>
        </div>
    </div>
</div>
@endif
@endsection
