# 📄 คำแนะนำการทดสอบ Pagination

## 🎯 การตั้งค่าใหม่

ตอนนี้ทุกหน้าใช้ **9 รายการต่อหน้า** เหมือนกัน:

| หน้า | รายการต่อหน้า | เมื่อเกิน 9 รายการ |
|------|-------------|-------------------|
| **ผลงาน** | 9 รายการ | จะขึ้นหน้า 2 |
| **บริการ** | 9 รายการ | จะขึ้นหน้า 2 |
| **แพคเกจ** | 9 รายการ | จะขึ้นหน้า 2 |

## 🧪 วิธีทดสอบ

### 1. รันคำสั่งสร้างข้อมูลทดสอบ
```bash
php artisan db:seed --class=PaginationTestSeeder
```

### 2. ผลลัพธ์ที่จะได้
- **ผลงาน**: 20 รายการ → 3 หน้า (9+9+2)
- **บริการ**: 15 รายการ → 2 หน้า (9+6)  
- **แพคเกจ**: 12 รายการ → 2 หน้า (9+3)

### 3. ทดสอบด้วยตัวเอง
เพิ่มข้อมูลใหม่:
- **1-9 รายการ** = แสดงหน้าเดียว (ไม่มี pagination)
- **10+ รายการ** = แสดงปุ่มหน้า 1, 2, 3...

## 🎯 ตัวอย่างการทำงาน

### ผลงาน (Activities)
- หน้า 1: รายการ 1-9
- หน้า 2: รายการ 10-18  
- หน้า 3: รายการ 19-20

### บริการ (Services)
- หน้า 1: รายการ 1-9
- หน้า 2: รายการ 10-15

### แพคเกจ (Packages)
- หน้า 1: รายการ 1-9
- หน้า 2: รายการ 10-12

## ✅ สิ่งที่จะเห็น

1. **ข้อมูลสถิติ**: "แสดง 1 ถึง 9 จากทั้งหมด 20 รายการ"
2. **ปุ่มหน้า**: [1] [2] [3] พร้อมลูกศรซ้าย-ขวา
3. **Jump to Page**: Dropdown เลือกหน้าโดยตรง
4. **หน้าปัจจุบัน**: แสดงหน้า X / Y ข้างช่องค้นหา

## 🔧 การปรับแต่ง

หากต้องการเปลี่ยนจำนวนรายการต่อหน้า แก้ไขใน:
- `app/Http/Controllers/AdminController.php`
- เปลี่ยน `paginate(9)` เป็นจำนวนที่ต้องการ

ตัวอย่าง:
- `paginate(5)` = 5 รายการต่อหน้า (เกิน 5 ขึ้นหน้า 2)
- `paginate(15)` = 15 รายการต่อหน้า (เกิน 15 ขึ้นหน้า 2)
