@extends('layouts.app')

@section('title', $activity->title . ' - ผลงาน - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ'))

@section('content')
<!-- Hero Section with Cover Image -->
@php
    $heroCoverImage = $activity->images->where('is_cover', true)->first() ?? $activity->images->first();
    $heroCoverImagePath = $heroCoverImage ? $heroCoverImage->image_path : $activity->image;
@endphp
<section class="hero-section" style="background-image: linear-gradient(rgba(44, 62, 80, 0.7), rgba(44, 62, 80, 0.7)), url('{{ asset('storage/' . $heroCoverImagePath) }}'); background-size: cover; background-position: center;">
    <div class="container">
        <div class="text-center">
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb justify-content-center bg-transparent">
                    <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white-50">หน้าหลัก</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('activities') }}" class="text-white-50">ผลงาน</a></li>
                    <li class="breadcrumb-item active text-white" aria-current="page">{{ Str::limit($activity->title, 30) }}</li>
                </ol>
            </nav>
            <h1 class="display-4 fw-bold mb-4">{{ $activity->title }}</h1>
            <p class="lead">{{ $activity->description }}</p>
            <div class="d-flex justify-content-center gap-4 mt-4">
                <div class="text-white-50">
                    <i class="fas fa-calendar me-2"></i>
                    {{ $activity->activity_date->format('d/m/Y') }}
                </div>
                @if($activity->location)
                <div class="text-white-50">
                    <i class="fas fa-map-marker-alt me-2"></i>
                    {{ $activity->location }}
                </div>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Activity Detail Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-body p-4">


                        <!-- Main Image -->
                        <div class="mb-4">
                            @php
                                $mainImage = $activity->images->where('is_cover', true)->first() ?? $activity->images->first();
                                $mainImagePath = $mainImage ? $mainImage->image_path : $activity->image;
                            @endphp
                            <div class="img-container-fixed img-size-xlarge rounded shadow-sm">
                                <img src="{{ asset('storage/' . $mainImagePath) }}"
                                     class="img-fit-contain main-image"
                                     alt="{{ $activity->title }}"
                                     style="cursor: pointer;"
                                     data-bs-toggle="modal"
                                     data-bs-target="#imageModal"
                                     id="mainImage">
                            </div>
                        </div>

                        <!-- Image Gallery -->
                        @if($activity->images->count() > 0)
                        <div class="mb-4">
                            <h5 class="mb-3">แกลเลอรี่รูปภาพ</h5>
                            <div class="row g-2">
                                @foreach($activity->images->sortBy('sort_order') as $image)
                                <div class="col-md-3 col-4">
                                    <div class="gallery-image-container img-size-thumbnail">
                                        <img src="{{ asset('storage/' . $image->image_path) }}"
                                             class="img-fit-contain gallery-thumbnail"
                                             alt="{{ $image->caption ?? $activity->title }}"
                                             style="cursor: pointer;"
                                             onclick="changeMainImage('{{ asset('storage/' . $image->image_path) }}', '{{ $image->caption ?? $activity->title }}')"
                                             data-image="{{ asset('storage/' . $image->image_path) }}"
                                             data-caption="{{ $image->caption ?? $activity->title }}">
                                        @if($image->is_cover)
                                        <div class="position-absolute top-0 end-0 m-1">
                                            <span class="badge bg-primary">รูปหลัก</span>
                                        </div>
                                        @endif
                                        <div class="position-absolute bottom-0 start-0 w-100 bg-dark bg-opacity-75 text-white p-1 gallery-thumb-caption" style="font-size: 0.75rem;">
                                            {{ Str::limit($image->caption ?? 'รูปที่ ' . $loop->iteration, 20) }}
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <!-- Content -->
                        <div class="content">
                            <h3 class="mb-3">รายละเอียด</h3>
                            @if($activity->details)
                                <div class="mb-4">
                                    {!! nl2br(e($activity->details)) !!}
                                </div>
                            @else
                                <p class="text-muted mb-4">{{ $activity->description }}</p>
                            @endif

                            <!-- Activity Info -->
                            <div class="row g-3 mb-4">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center p-3 bg-light rounded">
                                        <i class="fas fa-calendar text-primary me-3 fa-lg"></i>
                                        <div>
                                            <small class="text-muted d-block">วันที่</small>
                                            <strong>{{ $activity->activity_date->format('d/m/Y') }}</strong>
                                        </div>
                                    </div>
                                </div>
                                @if($activity->location)
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center p-3 bg-light rounded">
                                        <i class="fas fa-map-marker-alt text-primary me-3 fa-lg"></i>
                                        <div>
                                            <small class="text-muted d-block">สถานที่</small>
                                            <strong>{{ $activity->location }}</strong>
                                        </div>
                                    </div>
                                </div>
                                @endif
                            </div>

                            <!-- Share Section -->
                            <div class="border-top pt-4">
                                <h5 class="mb-3">แชร์ผลงานนี้</h5>
                                <div class="d-flex gap-2">
                                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->fullUrl()) }}" 
                                       target="_blank" class="btn btn-outline-primary btn-sm">
                                        <i class="fab fa-facebook me-1"></i>Facebook
                                    </a>
                                    <a href="https://line.me/R/msg/text/?{{ urlencode($activity->title . ' - ' . request()->fullUrl()) }}" 
                                       target="_blank" class="btn btn-outline-success btn-sm">
                                        <i class="fab fa-line me-1"></i>Line
                                    </a>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="copyToClipboard()">
                                        <i class="fas fa-copy me-1"></i>คัดลอกลิงก์
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Contact Card -->
                <div class="card mb-4">
                    <div class="card-body text-center">
                        <h5 class="card-title">ต้องการความช่วยเหลือ?</h5>
                        <p class="card-text">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก</p>
                        <div class="d-grid gap-2">
                            <a href="{{ route('contact') }}" class="btn btn-primary">
                                <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                            </a>
                            <a href="tel:{{ $settings['contact_phone'] ?? '' }}" class="btn btn-outline-primary">
                                <i class="fas fa-phone me-2"></i>{{ $settings['contact_phone'] ?? '02-xxx-xxxx' }}
                            </a>
                        </div>
                        <small class="text-muted d-block mt-2">
                            <i class="fas fa-clock me-1"></i>
                            บริการตลอด 24 ชั่วโมง
                        </small>
                    </div>
                </div>

                <!-- Related Activities -->
                @if($relatedActivities->count() > 0)
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">ผลงานอื่นๆ</h5>
                    </div>
                    <div class="card-body p-0">
                        @foreach($relatedActivities as $related)
                        <div class="d-flex p-3 border-bottom">
                            <img src="{{ asset('storage/' . $related->image) }}" 
                                 class="rounded me-3" 
                                 style="width: 60px; height: 60px; object-fit: cover;" 
                                 alt="{{ $related->title }}">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    <a href="{{ route('activities.show', $related->id) }}" 
                                       class="text-decoration-none">
                                        {{ Str::limit($related->title, 40) }}
                                    </a>
                                </h6>
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ $related->activity_date->format('d/m/Y') }}
                                </small>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ $activity->title }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center p-0">
                @php
                    $modalMainImage = $activity->images->where('is_cover', true)->first() ?? $activity->images->first();
                    $modalMainImagePath = $modalMainImage ? $modalMainImage->image_path : $activity->image;
                @endphp
                <img src="{{ asset('storage/' . $modalMainImagePath) }}"
                     class="img-fluid w-100"
                     alt="{{ $activity->title }}"
                     id="modalImage">
            </div>
            @if($activity->images->count() > 1)
            <div class="modal-footer justify-content-center">
                <div class="d-flex gap-2 flex-wrap">
                    @foreach($activity->images->sortBy('sort_order') as $image)
                    <div class="img-container-fixed modal-thumb" style="width: 60px; height: 60px; cursor: pointer;" onclick="changeModalImage('{{ asset('storage/' . $image->image_path) }}', '{{ $image->caption ?? $activity->title }}')">
                        <img src="{{ asset('storage/' . $image->image_path) }}"
                             class="img-fit-contain img-thumbnail"
                             alt="{{ $image->caption ?? $activity->title }}">
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
function copyToClipboard() {
    navigator.clipboard.writeText(window.location.href).then(function() {
        // Show success message
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check me-1"></i>คัดลอกแล้ว';
        btn.classList.remove('btn-outline-secondary');
        btn.classList.add('btn-success');

        setTimeout(function() {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-secondary');
        }, 2000);
    });
}

function changeMainImage(imageSrc, caption) {
    // Change main image
    const mainImage = document.getElementById('mainImage');
    mainImage.src = imageSrc;
    mainImage.alt = caption;

    // Update modal image
    const modalImage = document.getElementById('modalImage');
    if (modalImage) {
        modalImage.src = imageSrc;
        modalImage.alt = caption;
    }

    // Remove active class from all thumbnails
    document.querySelectorAll('.gallery-thumbnail').forEach(function(thumb) {
        thumb.parentElement.classList.remove('active-thumbnail');
    });

    // Add active class to clicked thumbnail
    event.target.parentElement.classList.add('active-thumbnail');

    // Smooth scroll to main image
    mainImage.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

function changeModalImage(imageSrc, caption) {
    const modalImage = document.getElementById('modalImage');
    modalImage.src = imageSrc;
    modalImage.alt = caption;

    // Remove active class from all modal thumbnails
    document.querySelectorAll('.modal-thumb').forEach(function(thumb) {
        thumb.classList.remove('border-primary');
        thumb.style.borderWidth = '1px';
    });

    // Add active class to clicked modal thumbnail
    event.target.classList.add('border-primary');
    event.target.style.borderWidth = '3px';
}



document.addEventListener('DOMContentLoaded', function() {
    // Set first image as active by default
    const firstThumbnail = document.querySelector('.gallery-thumbnail');
    if (firstThumbnail) {
        firstThumbnail.parentElement.classList.add('active-thumbnail');
    }

    // Set first modal thumbnail as active
    const firstModalThumb = document.querySelector('.modal-thumb');
    if (firstModalThumb) {
        firstModalThumb.classList.add('border-primary');
        firstModalThumb.style.borderWidth = '3px';
    }


});
</script>
@endsection
