@if ($paginator->hasPages())
<div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
    <!-- Pagination Info -->
    <div class="pagination-info">
        <i class="fas fa-info-circle me-1"></i>
        แสดง <strong>{{ $paginator->firstItem() ?? 0 }}</strong> ถึง <strong>{{ $paginator->lastItem() ?? 0 }}</strong> 
        จากทั้งหมด <strong>{{ $paginator->total() }}</strong> รายการ
    </div>
    
    <!-- Simple Pagination Links -->
    <div class="d-flex align-items-center gap-2">
        {{-- Previous Page Link --}}
        @if ($paginator->onFirstPage())
            <span class="btn btn-sm btn-outline-secondary disabled">
                <i class="fas fa-chevron-left"></i> ก่อนหน้า
            </span>
        @else
            <a href="{{ $paginator->previousPageUrl() }}" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-chevron-left"></i> ก่อนหน้า
            </a>
        @endif

        {{-- Page Numbers --}}
        <div class="d-flex align-items-center gap-1">
            @for ($i = 1; $i <= $paginator->lastPage(); $i++)
                @if ($i == $paginator->currentPage())
                    <span class="btn btn-sm btn-primary">{{ $i }}</span>
                @else
                    <a href="{{ $paginator->url($i) }}" class="btn btn-sm btn-outline-secondary">{{ $i }}</a>
                @endif
            @endfor
        </div>

        {{-- Next Page Link --}}
        @if ($paginator->hasMorePages())
            <a href="{{ $paginator->nextPageUrl() }}" class="btn btn-sm btn-outline-primary">
                ถัดไป <i class="fas fa-chevron-right"></i>
            </a>
        @else
            <span class="btn btn-sm btn-outline-secondary disabled">
                ถัดไป <i class="fas fa-chevron-right"></i>
            </span>
        @endif
    </div>
    
    <!-- Page Info -->
    <div class="text-muted small">
        หน้า {{ $paginator->currentPage() }} / {{ $paginator->lastPage() }}
    </div>
</div>

<style>
.pagination-info {
    background: var(--bg-surface, #fff);
    border: 1px solid var(--border-light, #e2e8f0);
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 14px;
    color: var(--text-secondary, #64748b);
}

.btn-sm {
    padding: 6px 12px;
    font-size: 13px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn-sm:hover {
    transform: translateY(-1px);
}

@media (max-width: 768px) {
    .pagination-info {
        font-size: 12px;
        padding: 6px 8px;
    }
    
    .btn-sm {
        padding: 4px 8px;
        font-size: 12px;
    }
}
</style>

<script>
// Add smooth loading for pagination links
document.addEventListener('DOMContentLoaded', function() {
    const paginationLinks = document.querySelectorAll('.btn[href]');
    
    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Add loading state
            this.style.pointerEvents = 'none';
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            
            // Show loading overlay after a short delay
            setTimeout(() => {
                const overlay = document.createElement('div');
                overlay.className = 'loading-overlay show';
                overlay.innerHTML = `
                    <div class="text-center">
                        <div class="loading-spinner"></div>
                        <p class="mt-3 text-muted">กำลังโหลด...</p>
                    </div>
                `;
                document.body.appendChild(overlay);
            }, 100);
        });
    });
});
</script>
@endif
