@extends('layouts.app')

@section('title', 'แพคเกจ - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ'))

@section('content')
<!-- Page Header -->
<section class="hero-section">
    <div class="container">
        <div class="text-center">
            <h1 class="display-4 fw-bold mb-4">แพคเกจบริการ</h1>
            <p class="lead">แพคเกจบริการจัดงานศพที่ครบครันและเหมาะสมกับทุกครอบครัว</p>
        </div>
    </div>
</section>

<!-- Packages Section -->
<section class="py-5">
    <div class="container">
        @if($packages->count() > 0)
        <div class="row g-4">
            @foreach($packages as $package)
            <div class="col-md-6 col-lg-4">
                <div class="card package-card h-100 {{ $package->is_featured ? 'border-warning' : '' }}">
                    @if($package->is_featured)
                    <div class="card-header bg-warning text-dark text-center fw-bold">
                        <i class="fas fa-star me-2"></i>แพคเกจแนะนำ
                    </div>
                    @endif
                    
                    @if($package->image)
                    <img src="{{ asset('storage/' . $package->image) }}" class="card-img-top" alt="{{ $package->name }}" style="height: 200px; object-fit: cover;">
                    @else
                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                        <i class="fas fa-dove fa-4x text-muted"></i>
                    </div>
                    @endif

                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">{{ $package->name }}</h5>
                        <p class="card-text">{{ $package->description }}</p>

                        <div class="mb-3">
                            <h6>รายการที่รวมอยู่ในแพคเกจ:</h6>
                            <div class="features">
                                {!! nl2br(e($package->features)) !!}
                            </div>
                        </div>

                        <div class="mt-auto">
                            <div class="text-center mb-3">
                                @if($package->duration)
                                <div class="small text-muted mb-2">{{ $package->duration }}</div>
                                @endif
                                <small class="text-muted d-block">สอบถามราคาและรายละเอียดได้ที่เจ้าหน้าที่</small>
                            </div>
                            <a href="{{ route('contact') }}" class="btn btn-primary w-100">สอบถามแพคเกจ</a>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        @else
        <div class="text-center py-5">
            <i class="fas fa-dove fa-5x text-muted mb-4"></i>
            <h3 class="text-muted">ยังไม่มีแพคเกจ</h3>
            <p class="text-muted">กรุณาติดต่อเราเพื่อสอบถามแพคเกจบริการจัดงานศพ</p>
            <a href="{{ route('contact') }}" class="btn btn-primary">ติดต่อเรา</a>
        </div>
        @endif
    </div>
</section>

<!-- Comparison Section -->
@if($packages->count() > 1)
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="fw-bold">เปรียบเทียบแพคเกจ</h2>
            <p class="text-muted">เลือกแพคเกจที่เหมาะสมที่สุดสำหรับคุณ</p>
        </div>
        
        <div class="table-responsive">
            <table class="table table-bordered bg-white">
                <thead class="table-primary">
                    <tr>
                        <th>รายการ</th>
                        @foreach($packages as $package)
                        <th class="text-center">
                            {{ $package->name }}
                            @if($package->is_featured)
                            <br><span class="badge bg-warning text-dark">แนะนำ</span>
                            @endif
                        </th>
                        @endforeach
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>รายละเอียด</strong></td>
                        @foreach($packages as $package)
                        <td>{{ Str::limit($package->description, 100) }}</td>
                        @endforeach
                    </tr>
                    <tr>
                        <td><strong>ระยะเวลา</strong></td>
                        @foreach($packages as $package)
                        <td class="text-center">
                            @if($package->duration)
                            <small class="text-muted">{{ $package->duration }}</small>
                            @else
                            <small class="text-muted">-</small>
                            @endif
                        </td>
                        @endforeach
                    </tr>
                    <tr>
                        <td></td>
                        @foreach($packages as $package)
                        <td class="text-center">
                            <a href="{{ route('contact') }}" class="btn btn-primary">สอบถามแพคเกจนี้</a>
                        </td>
                        @endforeach
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</section>
@endif

<!-- Contact CTA Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="fw-bold mb-4">ต้องการความช่วยเหลือหรือไม่?</h2>
                <p class="lead mb-4">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก ติดต่อเราได้ตลอด 24 ชั่วโมง</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="{{ route('contact') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                    </a>
                    <a href="tel:{{ $settings['contact_phone'] ?? '' }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-phone me-2"></i>{{ $settings['contact_phone'] ?? '02-xxx-xxxx' }}
                    </a>
                </div>
                <div class="mt-4">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        บริการตลอด 24 ชั่วโมง ทุกวัน
                    </small>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
