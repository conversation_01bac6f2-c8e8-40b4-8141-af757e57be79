@extends('layouts.admin')

@section('title', 'แก้ไขบริการ - ระบบจัดการ')

@section('breadcrumb')
<li class="breadcrumb-item"><a href="{{ route('admin.services') }}">จัดการบริการ</a></li>
<li class="breadcrumb-item active">แก้ไขบริการ</li>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">แก้ไขบริการ: {{ $service->title }}</h1>
    <a href="{{ route('admin.services') }}{{ isset($page) && $page > 1 ? '?page=' . $page : '' }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>กลับ
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form action="{{ route('admin.services.update', $service->id) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    <input type="hidden" name="page" value="{{ $page ?? 1 }}">
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">ชื่อบริการ <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('title') is-invalid @enderror" 
                               id="title" name="title" value="{{ old('title', $service->title) }}" required>
                        @error('title')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">คำอธิบายสั้น <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3" required>{{ old('description', $service->description) }}</textarea>
                        @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="details" class="form-label">รายละเอียด</label>
                        <textarea class="form-control @error('details') is-invalid @enderror" 
                                  id="details" name="details" rows="5">{{ old('details', $service->details) }}</textarea>
                        @error('details')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="sort_order" class="form-label">ลำดับการแสดง</label>
                        <input type="number" class="form-control @error('sort_order') is-invalid @enderror"
                               id="sort_order" name="sort_order" value="{{ old('sort_order', $service->sort_order) }}" min="0">
                        @error('sort_order')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="image" class="form-label">รูปภาพ</label>
                        @if($service->image)
                        <div class="mb-2">
                            <img src="{{ asset('storage/' . $service->image) }}" alt="{{ $service->title }}" 
                                 class="img-thumbnail" style="max-width: 200px;">
                            <div class="form-text">รูปภาพปัจจุบัน</div>
                        </div>
                        @endif
                        <input type="file" class="form-control @error('image') is-invalid @enderror" 
                               id="image" name="image" accept="image/*">
                        @error('image')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB (เลือกไฟล์ใหม่หากต้องการเปลี่ยน)</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   value="1" {{ old('is_active', $service->is_active) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                เปิดใช้งาน
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>บันทึกการแก้ไข
                        </button>
                        <a href="{{ route('admin.services') }}{{ isset($page) && $page > 1 ? '?page=' . $page : '' }}" class="btn btn-secondary">ยกเลิก</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">ข้อมูลบริการ</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>สร้างเมื่อ:</strong></td>
                        <td>{{ $service->created_at->format('d/m/Y H:i') }}</td>
                    </tr>
                    <tr>
                        <td><strong>แก้ไขล่าสุด:</strong></td>
                        <td>{{ $service->updated_at->format('d/m/Y H:i') }}</td>
                    </tr>
                    <tr>
                        <td><strong>สถานะ:</strong></td>
                        <td>
                            @if($service->is_active)
                            <span class="badge bg-success">เปิดใช้งาน</span>
                            @else
                            <span class="badge bg-secondary">ปิดใช้งาน</span>
                            @endif
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">การดำเนินการ</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('services') }}" class="btn btn-outline-primary" target="_blank">
                        <i class="fas fa-eye me-2"></i>ดูในเว็บไซต์
                    </a>
                    <form action="{{ route('admin.services.delete', $service->id) }}" method="POST" 
                          onsubmit="return confirm('คุณแน่ใจหรือไม่ที่จะลบบริการนี้?')">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="btn btn-outline-danger w-100">
                            <i class="fas fa-trash me-2"></i>ลบบริการ
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
